generator client {
  provider = "prisma-client-js"
}

generator json {
  provider = "prisma-json-types-generator"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserType {
  patient
  doctor
  admin
}

model User {
  id                    String                @id /// cognitoId
  type                  UserType              @default(patient)
  firstName             String
  lastName              String
  email                 String                @unique
  phone                 String?
  createdAt             DateTime              @default(now())
  deletedAt             DateTime?
  doctor                Doctor?
  patient               Patient?
  admin                 Admin?
  conversationWatches   ConversationWatcher[]
  conversationMessage   ConversationMessage[]
  conversations         Conversation[]        @relation("userConversations")
  lastMessageUser       Conversation[]        @relation("lastMessageUser")
  assignedConversations Conversation[]        @relation("assignedAdmin")
  canceledPatients      Patient[]             @relation("canceledBy")
  Referrals             Referral[]            @relation("Referrer")
  // ReferredBy          Referral[]            @relation("Referred")
  // Referral            Referral?
  ReferredBy            Referral[]            @relation("Referred")

  @@index([email], name: "email_index")
  @@index([firstName(ops: raw("gist_trgm_ops"))], type: Gist)
  @@index([lastName(ops: raw("gist_trgm_ops"))], type: Gist)
  @@index([email(ops: raw("gist_trgm_ops"))], type: Gist)
}

model State {
  id                 String                   @id @default(uuid())
  name               String?
  code               String?                  @unique @db.VarChar(2)
  enabled            Boolean                  @default(false)
  doctors            Doctor[]
  patients           Patient[]
  doctorStates       DoctorsOnState[]
  PatientWaitingList PatientWaitingList[]
  patientShipping    PatientShippingAddress[]
  pharmacyOnState    PharmacyOnState[]
}

enum DoctorRole {
  superDoctor
  doctor
}

model Doctor {
  id                            String  @id @default(uuid())
  userId                        String  @unique
  doseSpotClinicianId           String? @unique
  redrockSpringvilleClinicianId String? @unique
  redrockStGeorgeClinicianId    String? @unique

  doseSpotRegistrationStatus String?
  role                       DoctorRole         @default(doctor)
  middleName                 String?
  dateOfBirth                DateTime?          @db.Date
  address1                   String
  address2                   String?
  city                       String
  stateId                    String
  zip                        String
  primaryPhone               String?
  primaryFax                 String?
  npiNumber                  String?
  image                      String?
  active                     Boolean            @default(true)
  oooUntil                   DateTime?
  createdAt                  DateTime           @default(now())
  user                       User               @relation(fields: [userId], references: [id])
  state                      State              @relation(fields: [stateId], references: [id])
  prescribesIn               DoctorsOnState[]
  patients                   Patient[]
  historicAssignments        DoctorAssignment[]
  prescriptions              Prescription[]
  Treatment                  Treatment[]
  BulkTransfer               BulkTransfer[]
}

model BulkTransfer {
  id            String             @id @default(uuid())
  type          bulkTransferType?
  doctorId      String?
  pharmacyId    String?
  reason        String?
  rules         Json               @db.JsonB
  transferAt    DateTime           @default(now())
  revertAt      DateTime? /// this assumes OOO transfer
  status        bulkTransferStatus @default(pending)
  queuedJobs    Int                @default(0)
  completedJobs Int                @default(0)
  createdAt     DateTime           @default(now())
  completedAt   DateTime?
  doctor        Doctor?            @relation(fields: [doctorId], references: [id])
  pharmacy      Pharmacy?          @relation(fields: [pharmacyId], references: [id])
  assignments   DoctorAssignment[]

  @@index([type])
  @@index([status])
}

enum bulkTransferType {
  doctor
  pharmacy
}

enum bulkTransferStatus {
  pending
  inProgress
  completed
  reverting
  reverted
  failed
}

model DoctorAssignment {
  id                    String            @id @default(uuid())
  doctorId              String
  patientId             String
  previousAssignmentId  String?           @unique
  bulkTransferId        String?
  reasonForReassignment String?
  createdAt             DateTime          @default(now())
  previousAssignment    DoctorAssignment? @relation("PreviousAssignment", fields: [previousAssignmentId], references: [id])
  nextAssignment        DoctorAssignment? @relation("PreviousAssignment")
  doctor                Doctor            @relation(fields: [doctorId], references: [id])
  patient               Patient           @relation(fields: [patientId], references: [id])
  BulkTransfer          BulkTransfer?     @relation(fields: [bulkTransferId], references: [id])

  @@index([doctorId])
  @@index([patientId])
  @@index([bulkTransferId])
}

model DoctorsOnState {
  doctorId      String
  stateId       String
  limit         Int?
  licenseNumber String?
  doctor        Doctor  @relation(fields: [doctorId], references: [id])
  state         State   @relation(fields: [stateId], references: [id])

  @@id([doctorId, stateId])
}

enum patientGender {
  female
  male
}

enum patientStatus {
  onboardingPending
  onboardingRejected
  pendingUploadPhotos
  onboardingCompleted
  pendingApprovalFromDoctor
  pendingPrescription
  activePrescription
  nonActivePrescription
  cancelled
  banned
  deleted
}

enum patientVerificationStatus {
  pending
  verified
  rejected
  revalidated
}

enum patientCancelationDiscountStatus {
  notUsed
  requested
  alreadyUsed
}

enum questionnaireType {
  onboarding
  followUp
}

model Questionnaire {
  id          String            @id @default(uuid())
  type        questionnaireType @default(onboarding)
  version     Int               @default(1)
  name        String
  description String?
  config      Json              @db.JsonB
  createdAt   DateTime          @default(now())

  patients        Patient[]
  PatientFollowUp PatientFollowUp[]

  @@unique([type, version])
}

model Patient {
  id                          String  @id @default(uuid())
  userId                      String  @unique
  stateId                     String
  questionnaireId             String
  doctorId                    String?
  pharmacyId                  String?
  stripeCustomerId            String? @unique
  doseSpotPatientId           String? @unique
  redrockSpringvillePatientId String? @unique
  redrockStGeorgePatientId    String? @unique

  intercomContactId        String?                           @unique
  birthDate                DateTime?                         @db.Date
  gender                   patientGender?
  height                   Float?
  weight                   Float?
  idPhoto                  String?
  facePhoto                String?
  ///--
  onboardingState          Json                              @default("{}") @db.JsonB
  questionnaire            Questionnaire                     @relation(fields: [questionnaireId], references: [id])
  ///--
  status                   patientStatus                     @default(onboardingPending)
  statusBeforeCancellation patientStatus?
  verificationStatus       patientVerificationStatus         @default(pending)
  rejectedStatus           String?
  rejectedReason           String?
  verifiedByUser           String?
  acceptedByUser           String?
  getPromotionsSMS         Boolean                           @default(false)
  createdAt                DateTime                          @default(now())
  updatedAt                DateTime?
  completedAt              DateTime?
  acceptedAt               DateTime?
  verifiedAt               DateTime?
  rejectedAt               DateTime?
  user                     User                              @relation(fields: [userId], references: [id])
  state                    State                             @relation(fields: [stateId], references: [id])
  doctor                   Doctor?                           @relation(fields: [doctorId], references: [id])
  desiredTreatments        PatientDesiredTreatment[]
  shippingAddresses        PatientShippingAddress[]
  paymentMethods           PatientPaymentMethod[]
  prescriptions            Prescription[]
  historicAssignments      DoctorAssignment[]
  pharmacy                 Pharmacy?                         @relation(fields: [pharmacyId], references: [id])
  conversations            Conversation[]
  treatment                Treatment[]
  cancelationReason        String?
  cancelationNote          String?
  canceledByUserId         String?
  canceledBy               User?                             @relation(fields: [canceledByUserId], references: [id], name: "canceledBy")
  canceledAt               DateTime?
  cancelationDiscount      patientCancelationDiscountStatus? @default(notUsed)
  promoCoupon              String?

  referralCode String? @default(cuid()) // next: @unique & required

  PatientFollowUp PatientFollowUp[]
  AuditLog        AuditLog[]
  Outbox          Outbox[]
  subscriptions   Subscription[]
  ShipmentUpdate  ShipmentUpdate[]

  @@index([onboardingState(ops: JsonbPathOps)], type: Gin)
  @@index([stripeCustomerId])
  @@index([intercomContactId])
  @@index([status, pharmacyId])
  @@index([status, doctorId, stateId], name: "patient_filter_idx")
  @@index([status])
  @@index([status, createdAt])
  @@index([canceledByUserId])
}

model PatientShippingAddress {
  id        String   @id @default(uuid())
  patientId String
  address1  String
  address2  String?
  city      String
  stateId   String
  zip       String
  default   Boolean? @default(false)
  createdAt DateTime @default(now())
  patient   Patient  @relation(fields: [patientId], references: [id])
  state     State    @relation(fields: [stateId], references: [id])

  @@index([patientId])
}

model PatientPaymentMethod {
  id        String   @id @default(uuid())
  stripeId  String   @unique
  patientId String
  type      String?
  data      Json?    @db.JsonB
  default   Boolean? @default(false)
  createdAt DateTime @default(now())
  patient   Patient  @relation(fields: [patientId], references: [id])

  @@index([patientId])
  @@index([patientId, default])
  @@index([data(ops: JsonbPathOps)], type: Gin)
}

model PatientDesiredTreatment {
  patientId String
  productId String
  vials     Int     @default(1)
  patient   Patient @relation(fields: [patientId], references: [id])
  product   Product @relation(fields: [productId], references: [id])

  @@id([patientId, productId])
}

enum ProductForms {
  injectable
  oral
  tablet
}

enum ProductTypes {
  core
  additional
}

enum ProductGenericName {
  semaglutide
  tirzepatide
  ondansetron
}

model ProductCategory {
  id               String                     @id @default(uuid())
  name             String
  form             String?
  label            String?
  tags             String?
  shortDescription String?
  description      String?
  image            String?
  customCard       String?
  enabled          Boolean                    @default(true)
  order            Int                        @default(0)
  createdAt        DateTime                   @default(now())
  updatedAt        DateTime                   @updatedAt
  products         ProductToProductCategory[]
}

model ProductToProductCategory {
  productId         String
  productCategoryId String
  assignedAt        DateTime @default(now())

  product         Product         @relation(fields: [productId], references: [id])
  productCategory ProductCategory @relation(fields: [productCategoryId], references: [id])

  @@id([productId, productCategoryId])
}

// Products actually map to Stripe Prices (from Products)
model Product {
  id                      String   @id
  pharmacyId              String
  defaultPriceId          String?  @unique
  name                    String
  image                   String?
  description             String
  learnMore               String?
  isCore                  Boolean  @default(true)
  isAvailableInOnboarding Boolean  @default(true)
  customCard              String?
  active                  Boolean?

  genericName              ProductGenericName         @default(semaglutide)
  form                     ProductForms               @default(injectable)
  tags                     String?
  type                     ProductTypes               @default(core)
  label                    String?
  onboardingLabel          String?
  order                    Int                        @default(0)
  notice                   String?
  supplyLength             String?
  weightLossMultiplier     Float?
  /// [ProductMetadataType]
  metadata                 Json?                      @db.JsonB
  createdAt                DateTime                   @default(now())
  defaultPrice             ProductPrice?              @relation(fields: [defaultPriceId], references: [id], name: "defaultPrice")
  prescriptions            Prescription[]
  patientDesiredTreatments PatientDesiredTreatment[]
  productPrice             ProductPrice[]
  pharmacy                 Pharmacy                   @relation(fields: [pharmacyId], references: [id])
  productCategories        ProductToProductCategory[]

  @@index([isCore])
  @@index([active])
}

model ProductPriceEquivalenceGroup {
  id   String  @id @default(uuid())
  name String?

  productPrices ProductPrice[]
}

model ProductPrice {
  id                 String  @id
  productId          String
  equivalenceGroupId String?
  name               String
  active             Boolean @default(false)
  unit_amount        Int
  // Extracted from metadata
  label              String?
  description        String?
  phase              Int     @default(1)
  milligrams         Float? ///CHECK! maybe rename to dose or dosage?
  dosageLabel        String?

  dosageDescription       String?
  dosageTimeframe         String?
  dosageAdditionalMessage String?

  patientDirections String?
  compoundName      String?
  additiveBenefit   String?

  /// [ProductPriceMetadataType]
  metadata          Json?                         @db.JsonB
  created           DateTime                      @default(now())
  product           Product                       @relation(fields: [productId], references: [id])
  defaultFor        Product?                      @relation("defaultPrice")
  prescription      Prescription[]
  initialTreatments Treatment[]                   @relation("InitialProductPrice")
  topTreatments     Treatment[]                   @relation("TopProductPrice")
  equivalenceGroup  ProductPriceEquivalenceGroup? @relation(fields: [equivalenceGroupId], references: [id])
  externalMappings  ProductPriceMapping[]

  @@index([productId, equivalenceGroupId])
}

model Pharmacy {
  id                  String                @id @default(uuid())
  doseSpotPharmacyId  String                @unique
  slug                String?               @unique
  name                String
  enabled             Boolean               @default(false)
  enableApi           Boolean?
  regularPriority     Int                   @default(0)
  usingGLP1Priority   Int                   @default(0)
  color               String?
  metadata            Json?                 @db.JsonB
  createdAt           DateTime              @default(now())
  Product             Product[]
  Patient             Patient[]
  Prescription        Prescription[]
  PharmacyOnState     PharmacyOnState[]
  BulkTransfer        BulkTransfer[]
  PharmacyIntegration PharmacyIntegration[]
  ShipmentUpdate      ShipmentUpdate[]
}

model PharmacyOnState {
  pharmacyId String
  stateId    String
  pharmacy   Pharmacy @relation(fields: [pharmacyId], references: [id])
  state      State    @relation(fields: [stateId], references: [id])

  @@id([pharmacyId, stateId])
}

model ProductPriceMapping {
  id             String       @id @default(uuid())
  externalId     String
  productPriceId String
  name           String?
  metadata       Json?        @db.JsonB
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  productPrice   ProductPrice @relation(fields: [productPriceId], references: [id])

  @@unique([externalId, productPriceId])
}

enum prescriptionStatus {
  draft // just created
  queued // records created, WILL BE attempted
  open // payment attempted
  paid // payment successful, received from stripe webhook
  failed // payment failed, received from stripe webhook
  uncollectible // payment failed multiple times, received from stripe webhook
  voided // payment voided, we treat it as canceleed, received from stripe webhook
}

// For the moment, no integration with DoseSpot, so this one status is marked manually
enum prescriptionDosespotStatus {
  Requested // manually set by the doctor after prescribing in DoseSpot
}

model Prescription {
  id                     String                      @id @default(uuid())
  doctorId               String
  patientId              String
  productId              String
  refill                 Int?
  productPriceId         String
  treatmentId            String
  pharmacyId             String
  doseSpotPrescriptionId String?
  stripeInvoiceId        String?
  stripeInvoiceItemId    String?
  stripeCouponId         String?
  status                 prescriptionStatus          @default(draft)
  doseSpotStatus         prescriptionDosespotStatus?
  lastError              Json?                       @db.JsonB
  createdAt              DateTime                    @default(now())
  updatedAt              DateTime?
  //--
  patient                Patient                     @relation(fields: [patientId], references: [id])
  doctor                 Doctor                      @relation(fields: [doctorId], references: [id])
  product                Product                     @relation(fields: [productId], references: [id])
  productPrice           ProductPrice                @relation(fields: [productPriceId], references: [id])
  treatment              Treatment                   @relation(fields: [treatmentId], references: [id])
  pharmacy               Pharmacy                    @relation(fields: [pharmacyId], references: [id])
  pharmacyIntegration    PharmacyIntegration[]

  @@index([stripeInvoiceId])
  @@index([stripeInvoiceItemId])
  @@index([status])
  @@index([patientId])
  @@index([treatmentId])
}

model Treatment {
  id                    String  @id @default(uuid())
  patientId             String
  doctorId              String
  initialProductPriceId String
  topProductPriceId     String?
  transferredTo         String? @unique

  isCore        Boolean      @default(true)
  currentRefill Int          @default(0)
  refills       Int
  vials         Int          @default(1)
  refillSystem  refillSystem @default(static)
  couponCode    String?
  state         Json         @default("{}") @db.JsonB
  status        String
  notes         String       @default("")

  createdAt          DateTime  @default(now())
  updatedAt          DateTime?
  nextEventIn        DateTime?
  nextNotificationIn DateTime?
  completedAt        DateTime?
  failedAt           DateTime?
  uncollectibleAt    DateTime?
  cancelledAt        DateTime?
  deletedAt          DateTime?

  initialProductPrice       ProductPrice          @relation(name: "InitialProductPrice", fields: [initialProductPriceId], references: [id])
  topProductPrice           ProductPrice?         @relation(name: "TopProductPrice", fields: [topProductPriceId], references: [id])
  patient                   Patient               @relation(fields: [patientId], references: [id])
  doctor                    Doctor                @relation(fields: [doctorId], references: [id])
  transferredToTreatment    Treatment?            @relation(name: "TransferredTo", fields: [transferredTo], references: [id])
  transferredFromTreatments Treatment[]           @relation("TransferredTo")
  prescription              Prescription[]
  patientFollowUp           PatientFollowUp[]
  conversationMessage       ConversationMessage[]

  @@index([isCore])
  @@index([status])
  @@index([isCore, status])
  @@index([patientId, isCore, status, deletedAt])
}

// -- Chat

enum ConversationStatus {
  active
  needsReply /// if the patient has not replied in 24 hours, we need to send a email
  followup
  open /// for admin-doctor conversations - conversation is open and active
  closed /// for admin-doctor conversations - conversation is closed by admin
}

enum ConversationMessageType {
  message
  system
  doctorNote
}

enum ConversationMessageContentType {
  text
  image
  file
}

enum ConversationType {
  patientDoctor
  doctorAdmin
}

model Conversation {
  id                 String                @id @default(uuid())
  userId             String // point to the patient user (removed @unique)
  patientId          String // removed @unique to allow multiple conversations per patient
  status             ConversationStatus    @default(active)
  type               ConversationType      @default(patientDoctor)
  assignedAdminId    String? // for admin assignment in doctorAdmin conversations
  lastMessageText    String?
  lastMessageFrom    String?
  createdAt          DateTime              @default(now())
  updatedAt          DateTime?             @updatedAt
  closedAt           DateTime? // for tracking when admin closes conversation
  messages           ConversationMessage[]
  watcher            ConversationWatcher[]
  user               User                  @relation("userConversations", fields: [userId], references: [id])
  lastMessageUser    User?                 @relation("lastMessageUser", fields: [lastMessageFrom], references: [id])
  patient            Patient               @relation(fields: [patientId], references: [id])
  assignedAdmin      User?                 @relation("assignedAdmin", fields: [assignedAdminId], references: [id])
  ConversationRouter ConversationRouter[]

  @@unique([patientId, type]) // Composite unique constraint: one conversation per patient per type
  @@index([type])
  @@index([status])
  @@index([patientId, status])
  @@index([lastMessageFrom])
  @@index([assignedAdminId])
}

model ConversationMessage {
  id                   String                         @id @default(uuid())
  conversationId       String
  treatmentId          String?
  conversationRouterId String?
  userId               String? // patients or doctors, many doctors can be in a conversation, in the past
  type                 ConversationMessageType        @default(message)
  contentType          ConversationMessageContentType @default(text)
  content              String
  createdAt            DateTime                       @default(now())
  conversation         Conversation                   @relation(fields: [conversationId], references: [id])
  treatment            Treatment?                     @relation(fields: [treatmentId], references: [id])
  conversationRouter   ConversationRouter?            @relation(fields: [conversationRouterId], references: [id])
  user                 User?                          @relation(fields: [userId], references: [id])

  @@index([conversationId])
  @@index([treatmentId])
  @@index([conversationRouterId])
  @@index([userId])
}

model ConversationWatcher {
  id             String       @id @default(uuid())
  conversationId String
  userId         String
  unreadMessages Int          @default(0)
  updatedAt      DateTime?
  needsReply     Boolean      @default(false)
  conversation   Conversation @relation(fields: [conversationId], references: [id])
  user           User         @relation(fields: [userId], references: [id])

  @@unique([conversationId, userId])
  @@index([conversationId])
  @@index([userId])
  @@index([unreadMessages])
  @@index([conversationId, unreadMessages])
}

enum AdminRole {
  superAdmin
  admin
  doctor
}

model Admin {
  id        String    @id @default(uuid())
  userId    String    @unique
  role      AdminRole @default(admin)
  createdAt DateTime? @default(now())
  user      User      @relation(fields: [userId], references: [id])
}

model PatientWaitingList {
  id        String   @id @default(uuid())
  stateId   String
  email     String   @unique
  state     State    @relation(fields: [stateId], references: [id])
  createdAt DateTime @default(now())
}

enum refillSystem {
  static
  scaling
  downscaling
}

enum PatientFollowUpStatus {
  scheduled
  completedByPatient
  reviewedByDoctor
  cancelled
}

enum PatientFollowUpAutoPrescribe {
  no
  currentDose
  increasedDose
}

model PatientFollowUp {
  id          String     @id @default(uuid())
  patientId   String
  treatmentId String?
  patient     Patient    @relation(fields: [patientId], references: [id])
  treatment   Treatment? @relation(fields: [treatmentId], references: [id])

  scheduledAt   DateTime
  status        PatientFollowUpStatus
  autoPrescribe PatientFollowUpAutoPrescribe?

  questionnaireId    String
  questionnaire      Questionnaire @relation(fields: [questionnaireId], references: [id])
  questionnaireState Json          @default("{}") @db.JsonB

  completedAt             DateTime?
  createdAt               DateTime  @default(now())
  updatedAt               DateTime? @updatedAt
  isStartNotificationSent Boolean   @default(false)

  //unique key in migration file
  // CREATE UNIQUE INDEX "patient_scheduled_followup_unique"
  // ON "PatientFollowUp"("patientId", "status")
  // WHERE status = 'scheduled'

  @@index([status])
  @@index([patientId, status])
  @@index([treatmentId])
}

model AuditLog {
  id                String @id @default(uuid())
  actorType         String
  actorId           String
  actorExtraDetails Json?

  resourceType String
  resourceId   String

  action  String
  details Json?

  patientId String?
  Patient   Patient? @relation(fields: [patientId], references: [id])

  createdAt DateTime @default(now())

  @@index([patientId])
}

enum ReferralStatus {
  PENDING
  COMPLETED
}

model Referral {
  id             String  @id @default(cuid())
  referrerUserId String
  referredUserId String? @unique()

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  referralCode String
  status       ReferralStatus @default(PENDING)

  invitedUserId    String?
  invitedUserEmail String?

  referrer User  @relation("Referrer", fields: [referrerUserId], references: [id])
  referred User? @relation("Referred", fields: [referredUserId], references: [id])
  // User     User?   @relation(fields: [userId], references: [id])
  // userId   String?
  // User     User?   @relation(fields: [userId], references: [id])

  @@index([status])
  @@index([referrerUserId])
}

enum OutboxStatus {
  PENDING
  PROCESSING
  DONE
  FAILED
  ON_HOLD
  DUPLICATE
}

model Outbox {
  id              String       @id @default(uuid())
  destination     String
  topic           String?
  payload         Json         @db.JsonB
  status          OutboxStatus @default(PENDING)
  retryCount      Int          @default(0)
  maxRetries      Int          @default(3)
  deduplicationId String?
  error           Json?        @db.JsonB
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt
  processedAt     DateTime?

  patientId String
  patient   Patient @relation(fields: [patientId], references: [id])

  @@index([status])
  @@index([retryCount])
  @@index([createdAt])
  @@index([destination, topic])
  @@index([patientId])
}

enum SubscriptionStatus {
  active
  canceled
  expired
}

model Subscription {
  id        String             @id @default(uuid())
  patientId String?
  name      String
  status    SubscriptionStatus @default(active)

  startDate DateTime
  endDate   DateTime?

  lastStripeInvoiceId   String?
  lastChargeSuccessDate DateTime?

  metadata  Json?    @db.JsonB
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  patient Patient? @relation(fields: [patientId], references: [id])

  @@unique([patientId, name])
}

enum conversationRouterStatus {
  pending
  processing
  processed
  closed
}

model ConversationRouter {
  id                         String                   @id @default(uuid())
  conversationId             String
  intercomId                 String?
  delayedUntil               DateTime
  messages                   Json?                    @db.JsonB
  reason                     String?
  inquiryTypes               Json?                    @db.JsonB
  relevantForPatientServices Boolean?
  relevantForDoctor          Boolean?
  status                     conversationRouterStatus @default(pending)
  createdAt                  DateTime                 @default(now())
  conversation               Conversation             @relation(fields: [conversationId], references: [id])
  conversationMessage        ConversationMessage[]

  @@index([status, delayedUntil])
  @@index([intercomId])
  @@index([reason])
  @@index([conversationId])
}

model PharmacyIntegration {
  id             String       @id @default(uuid())
  pharmacyId     String
  prescriptionId String
  orderId        String
  request        Json         @db.JsonB
  response       Json         @db.JsonB
  responseStatus Int
  createdAt      DateTime     @default(now())
  pharmacy       Pharmacy     @relation(fields: [pharmacyId], references: [id])
  prescription   Prescription @relation(fields: [prescriptionId], references: [id])

  @@index([prescriptionId])
  @@index([orderId])
  @@index([createdAt])
  @@index([response(ops: JsonbPathOps)], type: Gin)
}

model ShipmentUpdate {
  id             String   @id @default(uuid())
  patientId      String
  pharmacyId     String?
  status         String
  trackingNumber String
  trackingLink   String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @default(now())

  patient  Patient   @relation(fields: [patientId], references: [id])
  pharmacy Pharmacy? @relation(fields: [pharmacyId], references: [id])

  @@index([trackingNumber])
  @@index([status])
  @@index([updatedAt])
  @@index([patientId])
}
