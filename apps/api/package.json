{"name": "@willow/api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"purge": "git clean -xdf .cache.turbo dist node_modules", "clean": "git clean -xdf .cache .turbo dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "pnpm with-env nest start", "start:prod": "pnpm with-env node dist/main", "dev": "pnpm with-env bash -c './bin/check-stripe-key.sh && pnpm with-env nest start --watch'", "dev:debug": "pnpm with-env bash -c './bin/check-stripe-key.sh && pnpm with-env nest start --debug=0.0.0.0:${PORT_CLI_DEBUG:-9229} --watch'", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint:fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "typecheck": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk=9230 -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "cli": "pnpm with-env node -r ts-node/register/transpile-only -r tsconfig-paths/register ./src/cli.ts", "cli:debug": "pnpm with-env node --inspect=0.0.0.0 --inspect-brk=${PORT_CLI_DEBUG:-9230} -r ts-node/register/transpile-only -r tsconfig-paths/register ./src/cli.ts", "dev:stripe": "pnpm with-env stripe listen --forward-to localhost:${API_PORT:-8081}/stripe/webhook", "localtunnel": "pnpm with-env node bin/localtunnel.js", "with-env": "dotenv -e ../../${ENV_FILE:-.env} --"}, "dependencies": {"@aws-sdk/client-bedrock-runtime": "^3.699.0", "@aws-sdk/client-cognito-identity-provider": "^3.470.0", "@aws-sdk/client-ses": "^3.782.0", "@aws-sdk/client-sns": "^3.758.0", "@aws-sdk/client-sqs": "^3.758.0", "@hedger/nestjs-encryption": "^0.1.5", "@nest-lab/throttler-storage-redis": "^1.1.0", "@nestjs/cache-manager": "^2.1.1", "@nestjs/common": "^10.2.10", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.2.10", "@nestjs/event-emitter": "^2.0.3", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.3.0", "@nestjs/platform-socket.io": "^10.3.0", "@nestjs/schedule": "^4.1.0", "@nestjs/swagger": "^7.2.0", "@nestjs/terminus": "^10.2.0", "@nestjs/throttler": "^6.4.0", "@nestjs/websockets": "^10.3.0", "@prisma/client": "^6.7.0", "@prisma/extension-optimize": "^1.1.8", "@prisma/extension-read-replicas": "^0.4.1", "@prisma/instrumentation": "^6.7.0", "@segment/analytics-node": "^2.0.0", "@willow/auth": "workspace:^", "@willow/db": "workspace:^", "@willow/utils": "workspace:^", "amazon-cognito-identity-js": "^6.3.7", "aws-sdk": "^2.1547.0", "axios": "^1.7.9", "big.js": "^6.2.2", "cache-manager": "^5.3.1", "cache-manager-redis-store": "^3.0.1", "canvas": "^3.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cookie-parser": "^1.4.6", "customerio-node": "^4.2.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dayjs": "^1.11.13", "dd-trace": "^5.2.0", "drizzle-orm": "^0.29.1", "googleapis": "^148.0.0", "intercom-client": "^6.2.0", "ioredis": "^5.4.1", "jwks-rsa": "^3.1.0", "lodash": "^4.17.21", "nestjs-cls": "^5.2.0", "nestjs-pino": "^4.4.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pdfkit": "^0.17.0", "pg": "^8.11.3", "pino-http": "^10.4.0", "pino-pretty": "^13.0.0", "postgres": "^3.4.3", "ramda": "^0.29.1", "redlock": "5.0.0-beta.2", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "short-unique-id": "^5.2.0", "stripe": "^14.12.0", "taxjar": "^4.1.0", "tlds": "^1.254.0", "typescript": "catalog:", "uuid": "^9.0.0", "xstate": "^5.18.1", "zod": "catalog:"}, "devDependencies": {"@inquirer/search": "^3.0.1", "@nestjs/cli": "^10.4.2", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^10.2.10", "@swc/cli": "^0.1.63", "@swc/core": "^1.3.100", "@types/amazon-cognito-auth-js": "^1.3.4", "@types/big.js": "^6.2.2", "@types/cache-manager-redis-store": "^2.0.4", "@types/chance": "^1.1.6", "@types/cookie-parser": "^1.4.6", "@types/express": "^4.17.17", "@types/inquirer": "^9.0.8", "@types/ioredis": "^5.0.0", "@types/jest": "^29.5.2", "@types/lodash": "^4.17.7", "@types/multer": "^1.4.12", "@types/node": "^22.5.0", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/pg": "^8.10.9", "@types/ramda": "^0.29.9", "@types/supertest": "^2.0.12", "@types/table": "^6.3.2", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "chance": "^1.1.11", "csv-parse": "^5.5.6", "diff": "^7.0.0", "dotenv": "^16.4.5", "dotenv-cli": "^7.4.2", "eslint": "catalog:", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "inquirer": "^12.4.2", "jest": "^29.7.0", "localtunnel": "^2.0.2", "nest-commander": "^3.14.0", "ora": "^5.4.1", "prettier": "catalog:", "prisma": "^6.7.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "table": "^6.9.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.7.1"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^@modules/(.*)$": "<rootDir>/src/modules/$1", "^@adapters/(.*)$": "<rootDir>/src/adapters/$1", "^@test/(.*)$": "<rootDir>/test/$1"}}}