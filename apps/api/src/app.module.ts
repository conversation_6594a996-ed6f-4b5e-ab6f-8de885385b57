import { join } from 'path';
import { AdminModule } from '@modules/admin/admin.module';
import { AuditLogModule } from '@modules/audit-log/audit-log.module';
import { AuthModule } from '@modules/auth/auth.module';
import { SimpleThrottlerGuard } from '@modules/auth/guards/simple-throttler.guard';
import { ChatModule } from '@modules/chat/chat.module';
import { getClientIp } from '@modules/context/context.helpers';
import { ContextModule } from '@modules/context/context.module';
import { DoctorModule } from '@modules/doctor/doctor.module';
import { DosespotModule } from '@modules/dosespot/dosespot.module';
import { FollowUpModule } from '@modules/follow-up/follow-up.module';
import { HealthcheckModule } from '@modules/healthcheck/healthcheck.module';
import { IntegrationsModule } from '@modules/integrations/integrations.module';
import { IntercomModule } from '@modules/intercom/intercom.module';
import { OnboardingModule } from '@modules/onboarding/onboarding.module';
import { PatientModule } from '@modules/patient/patient.module';
import { PharmacyModule } from '@modules/pharmacy/pharmacy.module';
import { PrismaModule } from '@modules/prisma/prisma.module';
import { ProductModule } from '@modules/product/product.module';
import { ReferralModule } from '@modules/referral/referral.module';
import { SegmentModule } from '@modules/segment/segment.module';
import { SnsModule } from '@modules/shared/aws/sns/sns.module';
import { SharedModule } from '@modules/shared/shared.module';
import { ShipmentModule } from '@modules/shipment/shipment.module';
import { StateModule } from '@modules/state/state.module';
import { StripeModule } from '@modules/stripe/stripe.module';
import { TreatmentModule } from '@modules/treatment/treatment.module';
import { ThrottlerStorageRedisService } from '@nest-lab/throttler-storage-redis';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_GUARD } from '@nestjs/core';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { ThrottlerModule } from '@nestjs/throttler';
import Redis from 'ioredis';
import { ClsModule } from 'nestjs-cls';
import { LoggerModule } from 'nestjs-pino';

import { CustomerioModule } from './modules/customerio/customerio.module';
import { InsightsModule } from './modules/insights/insights.module';
import { OrchestrationModule } from './modules/shared/orchestration/orchestration.module';

@Module({
  imports: [
    ...(process.env.ENVIRONMENT === 'local'
      ? []
      : [
          LoggerModule.forRoot({
            pinoHttp: {
              formatters: {
                level: (label, level) => {
                  return { level, status: label.toUpperCase() };
                },
              },
              level: process.env.LOG_LEVEL || 'info',
            },
          }),
        ]),
    EventEmitterModule.forRoot({ wildcard: true }),
    ConfigModule.forRoot({
      envFilePath: process.env.ENV_FILE || '.env',
      isGlobal: true,
      load: [
        () => ({
          resourcesPath:
            process.env.ENVIRONMENT !== 'local'
              ? '/app/apps/api/dist/assets'
              : join(process.cwd(), 'resources', 'assets'),
        }),
      ],
    }),
    ThrottlerModule.forRoot({
      throttlers: [
        {
          name: 'default',
          ttl: 60000,
          limit: 100,
        },
      ],
      storage: new ThrottlerStorageRedisService(
        new Redis({
          host: process.env.REDIS_HOST || 'localhost',
          port: parseInt(process.env.REDIS_PORT || '6379'),
        }),
      ),
    }),
    ClsModule.forRoot({
      global: true,
      middleware: {
        // ClsMiddleware for all routes
        mount: true,
        setup: (cls, req) => {
          cls.set('ip', getClientIp(req));
        },
      },
    }),
    ContextModule,
    ScheduleModule.forRoot(),
    AuthModule,
    OnboardingModule,
    SnsModule,
    PatientModule,
    FollowUpModule,
    DoctorModule,
    ChatModule,
    SharedModule,
    StripeModule,
    HealthcheckModule,
    DosespotModule,
    AuditLogModule,
    SegmentModule,
    TreatmentModule,
    PrismaModule,
    AdminModule,
    ReferralModule,
    StateModule,
    ProductModule,
    PharmacyModule,
    IntegrationsModule,
    IntercomModule,
    InsightsModule,
    ShipmentModule,
    OrchestrationModule,
    CustomerioModule,
  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: SimpleThrottlerGuard,
    },
  ],
})
//
export class AppModule {}
