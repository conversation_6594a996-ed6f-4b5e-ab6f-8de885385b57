import {
  PatientDashboardProfile,
  PatientP<PERSON>istence,
  PatientProfile,
} from '@adapters/persistence/database/patient.persistence';
import { CognitoService } from '@modules/auth/cognito.service';
import { DosespotService } from '@modules/dosespot/dosespot.service';
import { DosespotPatient } from '@modules/dosespot/types/dosespot-patient';
import { UpdateProfileDto } from '@modules/patient/dto/update-profile.dto';
import {
  PrismaService,
  PrismaTransactionalClient,
} from '@modules/prisma/prisma.service';
import { ForbiddenError } from '@modules/shared/errors/forbidden.error';
import {
  getAge,
  getBirthDateToISO,
  getBirthDateToUS,
} from '@modules/shared/helpers/generic';
import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Prisma } from '@prisma/client';
import axios from 'axios';
import { isEqual } from 'date-fns';
import Stripe from 'stripe';

import { AuditLog, UpdatedBy } from '@willow/utils/src/audit-log';

import { AuditService } from '../audit-log/audit-log.service';
import {
  ContactUpdateProp,
  IntercomService,
} from '../intercom/intercom.service';
import { segmentIdentifyEvent, segmentTrackEvents } from '../shared/events';
import { PatientUpdatedEvent } from '../shared/events/patient-topic.definition';
import { OutboxerService } from '../shared/outboxer/outboxer.service';
import { SegmentIdentify, SegmentTrack } from '../shared/types/events';
import { StripeService } from '../stripe/service/stripe.service';
import { PatientRestoreSubscriptionUseCase } from './use-cases/patient-restore-subscription-use-case';

type UpdateProfileTraits = UpdateProfileDto &
  Partial<{
    age: number;
    birthdayTimestamp: string;
    birthdayString: string;
  }>;

type Address = {
  address1: string;
  address2?: string;
  city: string;
  state: string;
  zip: string;
};

@Injectable()
export class PatientService {
  constructor(
    private readonly patientPersistence: PatientPersistence,
    private readonly prismaService: PrismaService,
    private readonly patientRestoreSubscriptionUseCase: PatientRestoreSubscriptionUseCase,
    private readonly eventEmitter: EventEmitter2,
    private readonly cognitoService: CognitoService,
    private readonly stripeService: StripeService,
    private readonly dosespotService: DosespotService,
    private readonly auditService: AuditService,
    private readonly configService: ConfigService,
    private readonly outboxer: OutboxerService,
    private readonly intercomService: IntercomService,
  ) {}

  async getPatientData(userId: string): Promise<PatientDashboardProfile> {
    if (!userId) throw new Error('Missing/Falsy userId');

    // @todo review if we need to use the cache in this cases

    const profile = await this.patientPersistence.getDashboardProfile(userId);

    if (!profile)
      throw new ForbiddenError(
        `Onboarded patient not found for username: ${userId}`,
      );

    // @todo add similar check on the onboarding service. Makes more sense here that in each use-case
    if (
      ![
        'onboardingCompleted',
        'pendingApprovalFromDoctor',
        'pendingUploadPhotos',
        'pendingPrescription',
        'activePrescription',
        'nonActivePrescription',
        'cancelled',
      ].includes(profile.status)
    ) {
      throw new Error(
        `Invalid patient status: ${profile.status} for username: ${userId} in dashboard`,
      );
    }

    return profile;
  }

  async getPatientDataByPatientId(
    patientId: string,
  ): Promise<PatientDashboardProfile> {
    const patient = await this.patientPersistence.getPatientById(patientId);

    const profile = await this.patientPersistence.getDashboardProfile(
      patient.userId,
    );
    if (!profile)
      throw new ForbiddenError(
        `Patient not found for username: ${patient.userId}`,
      );

    return profile;
  }

  async unCancelPatient({
    patientId,
    restoredBy,
  }: {
    patientId: string;
    restoredBy: {
      type: 'DOCTOR' | 'ADMIN' | 'PATIENT';
      userId: string;
      id: string;
    };
  }) {
    return this.patientRestoreSubscriptionUseCase.execute({
      patientId,
      restoredBy,
    });
  }

  async updateProfile(
    profile: PatientDashboardProfile,
    data: UpdateProfileDto,
    options: {
      updatedBy: {
        type: 'PATIENT' | 'DOCTOR' | 'ADMIN' | 'SYSTEM' | 'COMMAND';
        id: string;
      };
    },
  ) {
    if (Object.keys(data).length === 0) {
      throw new Error('No data to update');
    }

    const updatePatient: Prisma.PatientUpdateInput = { user: { update: {} } };
    const stripeUpdate: Stripe.CustomerUpdateParams = {};
    const dosespotUpdate: Partial<DosespotPatient> = {};
    const traits: UpdateProfileTraits = {};
    const oldValues: UpdateProfileDto = {};
    const newValues: UpdateProfileDto = {};
    const intercomUpdate: ContactUpdateProp = {};

    // Prepare updates
    if (data.birthDate) {
      const [month, day, year] = data.birthDate.split('/').map(Number);
      const date = new Date(Date.UTC(year, month - 1, day));

      if (!isEqual(date, profile.birthDate)) {
        updatePatient.birthDate = date;
        dosespotUpdate.DateOfBirth = date;
        traits.birthDate = date.toISOString();
        newValues.birthDate = date.toISOString();
        traits.age = getAge(date);
        traits.birthdayTimestamp = getBirthDateToISO(date);
        traits.birthdayString = getBirthDateToUS(date);
        oldValues.birthDate = getBirthDateToISO(date);
      }
    }

    const nameUpdate = {
      firstName: data.firstName ? data.firstName : profile.user.firstName,
      lastName: data.lastName ? data.lastName : profile.user.lastName,
      hasChanges: false,
    };

    // Check if first name changed
    if (data.firstName && data.firstName !== profile.user.firstName) {
      newValues.firstName = data.firstName;
      updatePatient.user.update.firstName = data.firstName;
      dosespotUpdate.FirstName = data.firstName;
      traits.firstName = data.firstName;
      oldValues.firstName = profile.user.firstName;
      nameUpdate.hasChanges = true;
    }

    // Check if last name changed
    if (data.lastName && data.lastName !== profile.user.lastName) {
      newValues.lastName = data.lastName;
      updatePatient.user.update.lastName = data.lastName;
      dosespotUpdate.LastName = data.lastName;
      traits.lastName = data.lastName;
      oldValues.lastName = profile.user.lastName;
      nameUpdate.hasChanges = true;
    }

    // Update Stripe name only if either name changed
    if (nameUpdate.hasChanges) {
      const patientFullName = `${nameUpdate.firstName} ${nameUpdate.lastName}`;
      stripeUpdate.name = patientFullName;
      intercomUpdate.name = patientFullName;
    }

    if (data.phone && data.phone !== profile.user.phone) {
      newValues.phone = data.phone;
      updatePatient.user.update.phone = data.phone;
      stripeUpdate.phone = data.phone;
      dosespotUpdate.PrimaryPhone = data.phone;
      traits.phone = data.phone;
      oldValues.phone = profile.user.phone;
    }
    const emailUpdatePlanned = data.email && data.email !== profile.user.email;
    if (emailUpdatePlanned) {
      newValues.email = data.email;
      updatePatient.user.update.email = data.email;
      stripeUpdate.email = data.email;
      dosespotUpdate.Email = data.email;
      traits.email = data.email;
      oldValues.email = profile.user.email;
      intercomUpdate.email = data.email;
    }

    if (profile.verificationStatus === 'rejected') {
      updatePatient.verificationStatus = 'revalidated';
    }

    try {
      // Update Cognito (email only)
      if (data.email) {
        const cognitoUser = await this.cognitoService.getUser(data.email);
        if (cognitoUser?.['sub'] && cognitoUser['sub'] != profile.id) {
          throw new BadRequestException(
            'There is already an account with this email in use. Please contact Patient Services to continue',
          );
        }
        await this.cognitoService.updateUserEmail(
          profile.user.email,
          data.email,
        );
      }

      // Update DB
      await this.patientPersistence.update(profile.id, updatePatient);

      // Update Stripe
      if (Object.keys(stripeUpdate).length > 0) {
        await this.stripeService.updateCustomer(
          profile.stripeCustomerId,
          stripeUpdate,
        );
      }

      // Update DoseSpot
      if (
        Object.keys(dosespotUpdate).length > 0 &&
        profile.doseSpotPatientId &&
        profile?.doctor?.doseSpotClinicianId
      ) {
        await this.dosespotService.updatePatient(
          profile.doseSpotPatientId,
          dosespotUpdate,
          profile.doctor.doseSpotClinicianId,
        );
      }

      //Update intercom
      if (Object.keys(intercomUpdate).length > 0) {
        try {
          //Lets try to update the intercom contact using the intercomContactId because is cheaper
          //but if not posible (patients may not have intercomContactId) use the email
          if (profile.intercomContactId) {
            await this.intercomService.updateContactEmailByContactId(
              profile.intercomContactId,
              intercomUpdate,
            );
          } else {
            await this.intercomService.updateContactEmailByEmail(
              profile.user.email,
              intercomUpdate,
            );
          }
        } catch (error: any) {
          console.error('Error updating intercom contact', error);
        }
      }

      void this.auditService.append({
        patientId: profile.id,
        actorType: options.updatedBy.type as AuditLog['actorType'],
        actorId: options.updatedBy.id,
        resourceType: 'PATIENT',
        resourceId: profile.id,
        action: 'PATIENT_PROFILE_INFO_UPDATED',
        details: {
          old: {
            birthDate: getBirthDateToISO(profile.birthDate),
            firstName: profile.user.firstName,
            lastName: profile.user.lastName,
            phone: profile.user.phone,
            email: profile.user.email,
            status: profile.verificationStatus,
          },
          changes: {
            ...newValues,
            birthDate: newValues.birthDate
              ? getBirthDateToISO(newValues.birthDate)
              : undefined,
            status: updatePatient.verificationStatus?.toString(),
          },
        },
      });

      // Emit track and id events
      Object.keys(oldValues).forEach((field) => {
        const trackEvent: SegmentTrack = {
          event: segmentTrackEvents.profileUpdated.name,
          userId: profile.user.id,
          properties: {
            [`old${field.charAt(0).toUpperCase() + field.slice(1)}`]:
              oldValues[field],
            [`new${field.charAt(0).toUpperCase() + field.slice(1)}`]:
              data[field],
          },
        };
        this.eventEmitter.emit(
          segmentTrackEvents.profileUpdated.event,
          trackEvent,
        );
      });

      if (emailUpdatePlanned) {
        const trackEmailUpdatedEvent: SegmentTrack = {
          event: segmentTrackEvents.emailUpdated.name,
          userId: profile.user.id,
          properties: {
            oldEmail: oldValues.email,
            newEmail: newValues.email,
          },
        };
        this.eventEmitter.emit(
          segmentTrackEvents.emailUpdated.event,
          trackEmailUpdatedEvent,
        );
      }

      const identifyEvent: SegmentIdentify = {
        userId: profile.user.id,
        traits: traits,
      };

      this.eventEmitter.emit(
        segmentIdentifyEvent.analyticIdentify,
        identifyEvent,
      );

      return {
        patient: {
          ...profile,
          ...updatePatient,
          user: {
            ...profile.user,
            ...updatePatient.user.update,
          },
        },
      };
    } catch (error) {
      console.error('Error updating patient profile', error);
      // Rollback all changes
      const rollbackPatient: Prisma.PatientUpdateInput = {
        user: { update: {} },
      };
      const rollbackStripe: Stripe.CustomerUpdateParams = {};
      const rollbackDosespot: Partial<DosespotPatient> = {};

      if (oldValues.birthDate) {
        rollbackPatient.birthDate = new Date(oldValues.birthDate);
        rollbackDosespot.DateOfBirth = new Date(oldValues.birthDate);
      }
      if (oldValues.firstName) {
        rollbackPatient.user.update.firstName = oldValues.firstName;
        rollbackStripe.name = `${oldValues.firstName} ${profile.user.lastName}`;
        rollbackDosespot.FirstName = oldValues.firstName;
      }
      if (oldValues.lastName) {
        rollbackPatient.user.update.lastName = oldValues.lastName;
        rollbackStripe.name = `${profile.user.firstName} ${oldValues.lastName}`;
        rollbackDosespot.LastName = oldValues.lastName;
      }
      if (oldValues.phone) {
        rollbackPatient.user.update.phone = oldValues.phone;
        rollbackStripe.phone = oldValues.phone;
        rollbackDosespot.PrimaryPhone = oldValues.phone;
      }
      if (oldValues.email) {
        rollbackPatient.user.update.email = oldValues.email;
        rollbackStripe.email = oldValues.email;
        rollbackDosespot.Email = oldValues.email;

        await this.cognitoService.updateUser(profile.id, 'Patient', [
          { Name: 'email', Value: oldValues.email },
        ]);
      }

      await this.patientPersistence.update(profile.id, rollbackPatient);

      if (Object.keys(rollbackStripe).length > 0) {
        await this.stripeService.updateCustomer(
          profile.stripeCustomerId,
          rollbackStripe,
        );
      }

      if (
        Object.keys(rollbackDosespot).length > 0 &&
        profile.doseSpotPatientId &&
        profile?.doctor?.doseSpotClinicianId
      ) {
        await this.dosespotService.updatePatient(
          profile.doseSpotPatientId,
          rollbackDosespot,
          profile.doctor.doseSpotClinicianId,
        );
      }

      throw error;
    }
  }

  async updateShippingAddress(
    profile: PatientDashboardProfile,
    payload: {
      address: Omit<Address, 'state'>;
      shouldUpdateBillingAddress: boolean;
    },
    { updatedBy }: { updatedBy: UpdatedBy },
    { prisma }: { prisma?: PrismaTransactionalClient } = {},
  ) {
    const address = {
      ...payload.address,
      state: profile.state,
    };

    const db = prisma || this.prismaService;

    const defaultShipping = await db.patientShippingAddress.findFirst({
      where: { patientId: profile.id, default: true },
      include: { state: true },
    });

    await db.patientShippingAddress.updateMany({
      where: {
        patientId: profile.id,
        default: true,
      },
      data: {
        address1: address.address1,
        address2: address.address2 || null,
        stateId: address.state.id,
        city: address.city,
        zip: address.zip,
      },
    });

    if (payload.shouldUpdateBillingAddress) {
      await this.updateBillingAddress(
        profile,
        { address: { ...address, state: address.state.code } },
        { updatedBy },
        { prisma: db },
      );
    }

    //update dosespot if applicable
    if (profile.doseSpotPatientId) {
      await this.dosespotService.updatePatient(
        profile.doseSpotPatientId,
        {
          Address1: address.address1,
          Address2: address.address2 || '',
          City: address.city,
          State: address.state.code,
          ZipCode: address.zip,
        },
        profile.doctor.doseSpotClinicianId,
      );
    }
    await this.stripeService.updateShippingAddress(
      profile.stripeCustomerId,
      `${profile.user.firstName} ${profile.user.lastName}`,
      {
        line1: address.address1,
        line2: address.address2 || null,
        city: address.city,
        state: address.state.code,
        postal_code: address.zip,
      },
    );

    void this.auditService.append({
      patientId: profile.id,
      action: 'PATIENT_SHIPPING_ADDRESS_UPDATED',
      actorType: updatedBy.type,
      actorId: updatedBy.id,
      resourceType: 'PATIENT',
      resourceId: profile.id,
      details: {
        old: {
          address1: defaultShipping?.address1,
          address2: defaultShipping?.address2 || null,
          city: defaultShipping?.city,
          state: defaultShipping?.state?.code,
          zip: defaultShipping?.zip,
        },
        changes: {
          address1:
            defaultShipping?.address1 !== address.address1
              ? address.address1
              : undefined,
          address2:
            defaultShipping?.address2 !== address.address2
              ? address.address2
              : undefined,
          city:
            defaultShipping?.city !== address.city ? address.city : undefined,
          zip: defaultShipping?.zip !== address.zip ? address.zip : undefined,
        },
      },
    });

    const shippingAddress = {
      shippingAddress1: address.address1,
      shippingAddress2: address.address2 || null,
      shippingCity: address.city,
      shippingState: address.state.code,
      shippingZipcode: address.zip,
    };
    const shippingAddressIdentifyEvent: SegmentIdentify = {
      userId: profile.id,
      traits: {
        ...shippingAddress,
      },
    };
    this.eventEmitter.emit(
      segmentIdentifyEvent.analyticIdentify,
      shippingAddressIdentifyEvent,
    );
    const shippingAddressUpdated: SegmentTrack = {
      event: segmentTrackEvents.shippingUpdated.name,
      userId: profile.id,
      properties: {
        ...shippingAddress,
      },
    };
    this.eventEmitter.emit(
      segmentTrackEvents.shippingUpdated.event,
      shippingAddressUpdated,
    );
  }

  async updateBillingAddress(
    profile: PatientDashboardProfile,
    { address }: { address: Address },
    { updatedBy }: { updatedBy: UpdatedBy },
    { prisma }: { prisma?: PrismaTransactionalClient } = {},
  ) {
    const stripe = await this.stripeService.getCustomer(
      profile.stripeCustomerId,
    );

    // update stripe
    await this.stripeService.updateBillingAddress(profile.stripeCustomerId, {
      line1: address.address1,
      line2: address.address2 || null,
      city: address.city,
      state: address.state,
      postal_code: address.zip,
    });

    void this.auditService.append({
      patientId: profile.id,
      action: 'PATIENT_BILLING_ADDRESS_UPDATED',
      actorType: updatedBy.type as any,
      actorId: updatedBy.id,
      resourceType: 'PATIENT',
      resourceId: profile.id,
      details: {
        old: {
          address1: stripe?.address?.line1,
          address2: stripe?.address?.line2 || null,
          city: stripe?.address?.city,
          state: stripe?.address?.state,
          zip: stripe?.address?.postal_code,
        },
        changes: {
          address1:
            stripe?.address?.line1 !== address.address1
              ? address.address1
              : undefined,
          address2:
            stripe?.address?.line2 !== address.address2
              ? address.address2
              : undefined,
          city:
            stripe?.address?.city !== address.city ? address.city : undefined,
          state:
            stripe?.address?.state !== address.state
              ? address.state
              : undefined,
          zip:
            stripe?.address?.postal_code !== address.zip
              ? address.zip
              : undefined,
        },
      },
    });
  }

  async delete(id: string): Promise<'soft' | 'hard'> {
    const patient = await this.prismaService.patient.findFirst({
      where: { id },
      include: {
        user: true,
        conversations: {
          where: { type: 'patientDoctor' },
          include: {
            messages: { where: { type: 'message' } },
            watcher: true,
          },
        },
        prescriptions: true,
        shippingAddresses: true,
        paymentMethods: true,
        desiredTreatments: true,
        historicAssignments: true,
      },
    });

    const patientDoctorConversation = patient.conversations?.find(
      (c) => c.type === 'patientDoctor',
    );
    const hasChat = patientDoctorConversation?.messages.length > 0;
    const hasPrescription = patient.prescriptions.length > 0;

    if (hasChat || hasPrescription) {
      await this.prismaService.user.update({
        where: { id: patient.userId },
        data: { deletedAt: new Date() },
      });
      return 'soft';
    } else {
      try {
        if (patient.stripeCustomerId) {
          await this.stripeService
            .client()
            .customers.del(patient.stripeCustomerId);
        }

        await this.prismaService.$transaction(async (prisma) => {
          // Delete associated records
          if (patientDoctorConversation) {
            await prisma.conversationWatcher.deleteMany({
              where: { conversationId: patientDoctorConversation.id },
            });
            await prisma.conversation.delete({
              where: { id: patientDoctorConversation.id },
            });
          }

          await prisma.patientShippingAddress.deleteMany({
            where: { patientId: patient.id },
          });

          await prisma.patientPaymentMethod.deleteMany({
            where: { patientId: patient.id },
          });

          await prisma.patientDesiredTreatment.deleteMany({
            where: { patientId: patient.id },
          });

          await prisma.doctorAssignment.deleteMany({
            where: { patientId: patient.id },
          });

          // Delete the patient and user
          await prisma.patient.delete({ where: { id: patient.id } });
          await prisma.user.delete({ where: { id: patient.userId } });

          // Delete patient from Cognito
          await this.cognitoService.deleteUser(id);

          //delete patient from customer.io if in prod
          const isProd = this.configService.get('ENVIRONMENT') === 'production';
          if (isProd) {
            try {
              const siteId =
                this.configService.get<string>('CUSTOMERIO_SITE_ID');
              const trackingApiKey = this.configService.get<string>(
                'CUSTOMERIO_TRACKING_API_KEY',
              );

              await axios.delete(
                `https://track.customer.io/api/v1/customers/${id}`,
                {
                  headers: {
                    Authorization: `Basic ${Buffer.from(`${siteId}:${trackingApiKey}`).toString('base64')}`,
                    'Content-Type': 'application/json',
                  },
                },
              );
            } catch (e) {
              console.error('Error deleting patient from customer.io', e);
            }
          }
        });
        return 'hard';
      } catch (error) {
        console.error('Error during patient delete', error);
      }
    }
  }

  async enqueuePatientUpdateEvent(
    patientId: string,
    event: PatientUpdatedEvent['event'],
    patient: PatientProfile | null,
    { prisma }: { prisma: PrismaTransactionalClient } = {
      prisma: this.prismaService,
    },
  ) {
    let patientData = patient;
    if (!patientData)
      patientData = await prisma.patient.findUnique({
        where: { id: patientId },
        include: {
          user: true,
          doctor: true,
        },
      });

    if (!patientData) {
      throw new Error('Patient not found');
    }

    await this.outboxer.enqueue(
      patientData.id,
      'patient-updated',
      {
        event,
        patient: patientData,
      },
      { prisma },
    );
  }
}
