import { TrackChargeRefundedUseCase } from '@/modules/stripe/use-cases/track-charge-refunded.use-case';
import { StripeTrackInvoicePaymentFailedUseCase } from '@/modules/stripe/use-cases/track-invoice-payment-failed.use-case';
import { StripeTrackInvoiceUncollectibleUseCase } from '@/modules/stripe/use-cases/track-invoice-uncollectible.use-case';
import { PatientPaymentMethodPersistence } from '@adapters/persistence/database/patient-payment-method.persistence';
import { PatientPersistence } from '@adapters/persistence/database/patient.persistence';
import { QuestionnairePersistence } from '@adapters/persistence/database/questionnaire.persistence';
import { ShippingAddressPersistence } from '@adapters/persistence/database/shipping-address.persistence';
import { StatePersistence } from '@adapters/persistence/database/state.persistence';
import { AppCacheModule } from '@modules/cache/cache.module';
import { OnboardingStateService } from '@modules/onboarding/onboarding-state.service';
import { OnboardingEventEmitterService } from '@modules/onboarding/use-cases/onboarding-event-emitter.service';
import { PrismaModule } from '@modules/prisma/prisma.module';
import { UtmService } from '@modules/shared/services/utm.service';
import { StripeService } from '@modules/stripe/service/stripe.service';
import { StripeController } from '@modules/stripe/stripe.controller';
import { StripeChargeFailedUseCase } from '@modules/stripe/use-cases/charge-failed.use-case';
import { StripeAttemptOnboadingPaymentValidationUseCase } from '@modules/stripe/use-cases/payment-method-attached.use-case';
import { TreatmentModule } from '@modules/treatment/treatment.module';
import { forwardRef, Module } from '@nestjs/common';

import { AuditLogModule } from '../audit-log/audit-log.module';
import { ContextModule } from '../context/context.module';
import { ReferralModule } from '../referral/referral.module';
import { RecordPatientReferralUseCase } from '../referral/use-cases/record-patient-referral.use-case';
import { TryRedeemReferralAwardUseCase } from '../referral/use-cases/try-redeem-referral-award.use-case';
import { LoggerModule } from '../shared/logger/logger.module';
import { OutboxerModule } from '../shared/outboxer/outboxer.module';
import { StripeCreateInvoiceConsumer } from './stripe-create-invoice.consumer';
import { StripeConsumer } from './stripe.consumer';
import { StripeChargeDisputeCreatedUseCase } from './use-cases/charge-dispute-created.use-case';
import { CustomerDefaultPaymentMethodUpdatedUseCase } from './use-cases/customer-default-payment-method-updated.use-case';
import { CustomerSaveNewPaymentMethodUseCase } from './use-cases/customer-new-payment-method.use-case';
import { StripeTrackInvoicePaidUseCase } from './use-cases/track-invoice-paid.use-case';

@Module({
  controllers: [StripeController],
  imports: [
    PrismaModule,
    forwardRef(() => TreatmentModule),
    AuditLogModule,
    AppCacheModule,
    ReferralModule,
    ContextModule,
    OutboxerModule,
    LoggerModule,
  ],
  providers: [
    StripeAttemptOnboadingPaymentValidationUseCase,
    StripeTrackInvoicePaidUseCase,
    StripeChargeDisputeCreatedUseCase,
    StripeChargeFailedUseCase,
    TrackChargeRefundedUseCase,
    RecordPatientReferralUseCase,
    TryRedeemReferralAwardUseCase,
    StripeService,
    PatientPersistence,
    StatePersistence,
    ShippingAddressPersistence,
    QuestionnairePersistence,
    PatientPaymentMethodPersistence,
    OnboardingStateService,
    OnboardingEventEmitterService,
    UtmService,
    StripeTrackInvoiceUncollectibleUseCase,
    StripeTrackInvoicePaymentFailedUseCase,
    StripeConsumer,
    StripeCreateInvoiceConsumer,
    CustomerDefaultPaymentMethodUpdatedUseCase,
    CustomerSaveNewPaymentMethodUseCase,
  ],
  exports: [StripeService],
})
export class StripeModule {}
