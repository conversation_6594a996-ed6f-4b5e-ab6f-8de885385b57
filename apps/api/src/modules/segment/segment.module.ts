import { Module } from '@nestjs/common';

import { CustomerioModule } from '../customerio/customerio.module';
import { ListenersService } from './listeners/listeners.service';
import { SegmentController } from './segment.controller';
import { SegmentService } from './segment.service';

@Module({
  imports: [CustomerioModule],
  providers: [SegmentService, ListenersService],
  controllers: [SegmentController],
  exports: [SegmentService],
})
export class SegmentModule {}
