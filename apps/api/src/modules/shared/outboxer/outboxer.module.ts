import { PrismaModule } from '@/modules/prisma/prisma.module';
import { Module } from '@nestjs/common';

import { AppCacheModule } from '../../cache/cache.module';
import { SnsModule } from '../aws/sns/sns.module';
import { SqsModule } from '../aws/sqs/sqs.module';
import { OrchestrationModule } from '../orchestration/orchestration.module';
import { OutboxerService } from './outboxer.service';
import { OutboxerCleanWorker } from './workers/outboxer-clean.worker';
import { OutboxerSnsWorker } from './workers/outboxer-sns.worker';
import { OutboxerSqsWorker } from './workers/outboxer-sqs.worker';

@Module({
  imports: [
    PrismaModule,
    AppCacheModule,
    SqsModule,
    SnsModule,
    OrchestrationModule,
  ],
  providers: [
    OutboxerService,
    OutboxerSqsWorker,
    OutboxerSnsWorker,
    OutboxerCleanWorker,
  ],
  exports: [OutboxerService],
})
export class OutboxerModule {}
