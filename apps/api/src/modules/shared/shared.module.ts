import { AuditLogModule } from '@modules/audit-log/audit-log.module';
import { GoogleSheetsService } from '@modules/shared/services/google-sheets.service';
import { S3Service } from '@modules/shared/services/s3.service';
import { SlackService } from '@modules/shared/services/slack.service';
import { TaxjarService } from '@modules/shared/services/taxjar.service';
import { forwardRef, Module } from '@nestjs/common';

import { AuthModule } from '../auth/auth.module';
import { ContextModule } from '../context/context.module';
import { PrismaModule } from '../prisma/prisma.module';
import { LoggerModule } from './logger/logger.module';
import { LoggerFactory } from './logger/logger.service';
import { OrchestrationModule } from './orchestration/orchestration.module';
import { QuestionnairService } from './questionnaire/questionnaire.service';
import { UserForgotPasswordUseCase } from './use-cases/user-forgot-password-use.case';
import { UserResetPasswordWithTokenUseCase } from './use-cases/user-reset-password-with-token.use-case';

@Module({
  imports: [
    forwardRef(() => AuthModule),
    PrismaModule,
    AuditLogModule,
    OrchestrationModule,
    ContextModule,
    LoggerModule,
  ],
  providers: [
    UserForgotPasswordUseCase,
    UserResetPasswordWithTokenUseCase,
    S3Service,
    TaxjarService,
    SlackService,
    QuestionnairService,
    GoogleSheetsService,
    LoggerFactory,
  ],
  exports: [
    UserForgotPasswordUseCase,
    UserResetPasswordWithTokenUseCase,
    S3Service,
    TaxjarService,
    SlackService,
    GoogleSheetsService,
    LoggerFactory,
  ],
})
export class SharedModule {}
