import * as crypto from 'crypto';
import { ContextPopulationService } from '@/modules/context/context.service';
import {
  <PERSON>,
  Headers,
  HttpException,
  Param,
  Post,
  RawBodyRequest,
  Req,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

import { LoggerFactory, LoggerService } from '../../logger/logger.service';
import { SnsConsumerService, SnsRequestBody } from './sns-consumer.service';

// Certificate cache interface
interface CertificateCache {
  [url: string]: {
    cert: string;
    expiresAt: number;
  };
}

@Controller('sns')
export class SnsController {
  private readonly logger: LoggerService;
  private certificateCache: CertificateCache = {};
  // Cache TTL in milliseconds (24 hours)
  private readonly cacheTTL = 24 * 60 * 60 * 1000;
  private isSignatureVerificationEnabled: boolean;

  constructor(
    private readonly contextPopulate: ContextPopulationService,
    private readonly snsConsumerService: SnsConsumerService,
    private readonly config: ConfigService,
    private loggerFactory: LoggerFactory,
  ) {
    this.logger = this.loggerFactory.createLogger(SnsController.name);

    this.isSignatureVerificationEnabled = !this.config.get(
      'AWS_SNS_LOCALSTACK_ENDPOINT',
    );
    if (!this.isSignatureVerificationEnabled) {
      console.warn('SNS signature verification is disabled');
    }
  }

  fieldsForSignature(type: string): string[] {
    if (
      type === 'SubscriptionConfirmation' ||
      type === 'UnsubscribeConfirmation'
    ) {
      return [
        'Message',
        'MessageId',
        'SubscribeURL',
        'Timestamp',
        'Token',
        'TopicArn',
        'Type',
      ];
    } else if (type === 'Notification') {
      return [
        'Message',
        'MessageId',
        'Subject',
        'Timestamp',
        'TopicArn',
        'Type',
      ];
    } else {
      return [];
    }
  }

  /**
   * Verifies the signature of an SNS message to ensure it came from AWS
   */
  private async verifySnsSignature(payload: SnsRequestBody): Promise<boolean> {
    try {
      if (!payload.SigningCertURL || !payload.Signature) {
        this.logger.warn('Missing signature information in SNS payload');
        return false;
      }

      // Verify the certificate URL is from AWS
      const certUrl = new URL(payload.SigningCertURL);
      if (!certUrl.hostname.endsWith('.amazonaws.com')) {
        this.logger.warn(`Invalid certificate URL: ${payload.SigningCertURL}`);
        return false;
      }

      // Try to get certificate from cache first
      let cert: string;
      const now = Date.now();
      const cacheEntry = this.certificateCache[payload.SigningCertURL];

      if (cacheEntry && cacheEntry.expiresAt > now) {
        // Use cached certificate
        cert = cacheEntry.cert;
      } else {
        // Fetch the certificate
        const certResponse = await axios.get(payload.SigningCertURL);
        cert = certResponse.data;

        // Store in cache
        this.certificateCache[payload.SigningCertURL] = {
          cert,
          expiresAt: now + this.cacheTTL,
        };
      }

      // Build the message string to verify
      const messageToSign = this.fieldsForSignature(payload.Type)
        .filter((key) => Object.prototype.hasOwnProperty.call(payload, key))
        .map((key) => `${key}\n${payload[key]}\n`)
        .join('');

      // Verify the signature
      const verifier =
        payload.SignatureVersion === '1'
          ? crypto.createVerify('RSA-SHA1')
          : crypto.createVerify('RSA-SHA256');
      verifier.update(messageToSign, 'utf8');
      const isValid = verifier.verify(cert, payload.Signature, 'base64');

      if (!isValid) {
        this.logger.warn('SNS message signature verification failed');
      }

      return isValid;
    } catch (error) {
      this.logger.error(error, {}, 'Error verifying SNS signature');
      return false;
    }
  }

  parseBody(body: unknown): SnsRequestBody {
    if (typeof body === 'string') {
      try {
        return JSON.parse(body);
      } catch (error) {
        this.logger.error('Error parsing SNS message body', error);
        throw new HttpException('Invalid SNS message body', 400);
      }
    }
    return body as SnsRequestBody;
  }

  @Post('notification/topic/:topic/consumerGroup/:consumerGroup')
  async handleSnsNotification(
    @Headers('x-amz-sns-subscription-arn') subscriptionArn: string,
    @Param('topic') topic: string,
    @Param('consumerGroup') consumerGroup: string,
    @Req() req: RawBodyRequest<Request>,
  ) {
    const payload = this.parseBody(req.body);

    this.contextPopulate.populateActor({ type: 'SNS', topic, consumerGroup });

    try {
      const messageType = payload.Type;

      if (this.isSignatureVerificationEnabled) {
        const isSignatureValid = await this.verifySnsSignature(payload);
        if (!isSignatureValid) {
          throw new UnauthorizedException('Invalid SNS message signature');
        }
      }

      const handler = this.snsConsumerService.getConsumerHandler(
        topic,
        consumerGroup,
      );

      if (!handler) {
        this.logger.warn(
          `No handler found for topic: ${topic} - consumerGroup: ${consumerGroup}`,
        );
        throw new HttpException('No handler found for topic', 404);
      }

      switch (messageType) {
        case 'SubscriptionConfirmation':
          await axios.get(payload.SubscribeURL);
          this.logger.debug(
            `Subscription to topic ${topic} by consumerGroup ${consumerGroup} confirmed`,
          );
          return 'Subscription confirmation received';

        case 'Notification':
          await this.snsConsumerService.processMessage(
            topic,
            consumerGroup,
            payload,
          );
          return { success: true, message: 'Notification processed' };

        default:
          this.logger.warn(`Unsupported SNS message type: ${messageType}`);
          return { success: false, message: 'Unsupported message type' };
      }
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(error, {}, 'Error processing SNS message');
      throw new HttpException('Error processing message', 500);
    }
  }
}
