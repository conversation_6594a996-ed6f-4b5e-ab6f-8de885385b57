import { ContextModule } from '@/modules/context/context.module';
import { SNSClient } from '@aws-sdk/client-sns';
import { AppCacheModule } from '@modules/cache/cache.module';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DiscoveryModule } from '@nestjs/core';

import { LoggerModule } from '../../logger/logger.module';
import { SqsModule } from '../sqs/sqs.module';
import { SnsConsumerService } from './sns-consumer.service';
import { SnsController } from './sns.controller';
import { SnsService } from './sns.service';

@Module({
  imports: [
    ConfigModule,
    LoggerModule,
    DiscoveryModule,
    SqsModule,
    AppCacheModule,
    ContextModule,
  ],
  providers: [
    {
      provide: SNSClient,
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const endpoint = configService.get('AWS_SNS_LOCALSTACK_ENDPOINT');
        const region = configService.get('AWS_REGION');
        const accessKeyId = configService.get('AWS_ACCESS_KEY_ID');
        const secretAccessKey = configService.get('AWS_SECRET_ACCESS_KEY');

        return new SNSClient({
          region: endpoint ? 'us-east-1' : region,
          endpoint: endpoint,
          credentials: {
            accessKeyId: endpoint ? 'test' : accessKeyId,
            secretAccessKey: endpoint ? 'test' : secretAccessKey,
          },
        });
      },
    },
    SnsService,
    SnsConsumerService,
  ],
  controllers: [SnsController],
  exports: [SnsService, SnsConsumerService],
})
export class SnsModule {}
