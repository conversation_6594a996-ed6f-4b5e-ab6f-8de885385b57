import os from 'os';
import { Injectable } from '@nestjs/common';
import { ClsService } from 'nestjs-cls';

import { PrismaService } from '../prisma/prisma.service';

type Actor =
  | {
      type: 'DOCTOR' | 'PATIENT' | 'ADMIN';
      id: string;
      userId: string;
      firstName: string;
      lastName: string;
      impersonateBy?: string;
    }
  | {
      type: 'STRIPE';
      event: string;
    }
  | {
      type: 'INTERCOM';
    }
  | {
      type: 'COMMAND';
      computerUsername: string;
    }
  | {
      type: 'SNS';
      topic: string;
      consumerGroup: string;
    }
  | {
      type: 'SQS';
      queue: string;
    };

@Injectable()
export class ContextPopulationService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly cls: ClsService,
  ) {}

  public populateActor(actor: Actor) {
    return this.cls.set('actor', actor);
  }

  public populateActor_Command() {
    return this.cls.set<Actor>('actor', {
      type: 'COMMAND',
      computerUsername: os.userInfo().username,
    } satisfies Actor);
  }

  public async populateActor_User({
    userId,
  }: {
    userId: string;
    impersonatedBy?: string;
  }) {
    const user = await this.prisma.readReplica().user.findFirst({
      where: { id: userId },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        patient: { select: { id: true } },
        doctor: { select: { id: true } },
        admin: { select: { id: true } },
      },
    });
    const type = user.patient?.id
      ? 'PATIENT'
      : user.doctor?.id
        ? 'DOCTOR'
        : 'ADMIN';
    return this.cls.set('actor', {
      type,
      id: user.patient?.id || user.doctor?.id || user.admin?.id,
      userId: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
    } satisfies Actor);
  }
}

@Injectable()
export class ContextService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly cls: ClsService,
  ) {}

  getIP() {
    return this.cls.get<string | undefined>('ip');
  }

  getActor() {
    return this.cls.get<Actor>('actor');
  }
}
