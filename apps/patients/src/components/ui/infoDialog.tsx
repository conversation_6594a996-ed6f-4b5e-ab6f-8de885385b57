import type { VariantProps } from 'class-variance-authority';
import * as React from 'react';
import { cn } from '@/lib/utils';
import { cva } from 'class-variance-authority';

import { Button } from './button';
import { Dialog } from './dialog';

const dialogLabelsVariants = cva('', {
  variants: {
    variant: {
      default: 'text-denim-drk',
      dark: 'text-white',
    },
  },
  defaultVariants: {
    variant: 'default',
  },
});

interface InfoDialogProps extends VariantProps<typeof dialogLabelsVariants> {
  title: string;
  description?: string;
  confirmBtnText: string;
  confirmBtnClick: () => void;
  closeBtnText?: string;
  closeBtnClick?: () => void;
  children?: React.ReactNode;
}

const InfoDialog = ({
  title,
  description,
  confirmBtnText,
  confirmBtnClick,
  closeBtnText,
  closeBtnClick,
  children,
  variant,
}: InfoDialogProps) => (
  <Dialog variant={variant} className="mx-7 sm:mx-0">
    <div className="relative flex w-full flex-col gap-4 text-left sm:w-96">
      <div className="mb-4 flex flex-col items-center justify-center gap-6">
        <div
          className={cn(
            dialogLabelsVariants({ variant }),
            'self-stretch text-center text-4xl font-medium leading-10 md:text-5xl',
          )}
        >
          {title}
        </div>
        {children ? (
          children
        ) : (
          <div
            className={cn(
              dialogLabelsVariants({ variant }),
              'self-stretch text-center text-xl font-normal',
            )}
          >
            {description}
          </div>
        )}
      </div>

      <Button
        onClick={confirmBtnClick}
        size="lg"
        variant="electric"
        className="w-full"
      >
        <span> {confirmBtnText}</span>
      </Button>

      {closeBtnText && (
        <Button
          onClick={closeBtnClick}
          size="lg"
          variant={variant == 'dark' ? 'outlineWhite' : 'outlinePrimary'}
          className="w-full"
        >
          <span>{closeBtnText}</span>
        </Button>
      )}
    </div>
  </Dialog>
);

InfoDialog.displayName = 'InfoDialog';

export { InfoDialog };
