'use client';

import { useState } from 'react';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { OnboardingPhotoUpload } from '@/components/upload-id/OnboardingPhotoUpload';

import { IdentityVerificationModal } from '~/components/upload-id/IdentityVerificationModal';

const UploadIdPhoto = () => {
  const [showModal, setShowModal] = useState(false);

  return (
    <OnboardingTitle
      title="Upload a photo of your ID"
      subtitle="In order to legally prescribe medications, we need a valid government-issued ID that matches your name and date of birth."
    >
      <div className="flex items-center justify-center">
        <a
          href="#"
          className="text-white underline"
          onClick={() => setShowModal(true)}
        >
          Why do you need this?
        </a>
      </div>

      <OnboardingPhotoUpload type={'idPhoto'} />

      {showModal && (
        <IdentityVerificationModal onClose={() => setShowModal(false)} />
      )}
    </OnboardingTitle>
  );
};

export default UploadIdPhoto;
